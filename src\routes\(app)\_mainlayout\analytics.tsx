import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import { Navbar } from "@/components/layout/main/navbar";
import { useMediaQuery } from "react-responsive";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { <PERSON><PERSON><PERSON> } from "@/components/charts";
import { useGetAnalytics, useGetQuizAnalytics } from "@/lib/queries/user.query";
import { useGetAllQuizzes } from "@/lib/queries/tests.query";
import { AnalyticsEmptyState } from "@/components/common";
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import ResourcePagination from "@/components/ui/resource-pagination";
import { ICONS } from "@/lib/assets/images";
import PrepBanner from "@/components/common/PrepBanner";
import TestSelectionModal from "@/components/tests/TestSelectionModel";
import { getMockTests, MockTest } from "@/features/tests/custom.api";

// Import analytics components
import {
	// AnalyticsCard,
	AnalyticsSection,
	SubjectDetailModal,
	// FuturePredictionComingSoon,
	AnalyticsMainLoading,
	// AnalyticsFutureLoading,

	// Types and utilities
	TimeFrame,
	TimeFrameData,
	CHART_COLORS,
	TIME_FRAME_OPTIONS,
	transformDataToAnalytics,
	transformSubjectData,
	extractSubjectsFromDivision,
	extractMcqsTypeFromDivision,
	extractDifficultyTypeFromDivision,
	// formatName,
} from "@/components/layout/analytics";

const Page = () => {
	const isDesktop = useMediaQuery({ minWidth: 1024 });
	const navigate = useNavigate();

	// State for modal and time frame selection
	const [selectedSubjectAndType, setSelectedSubjectAndType] = useState<{
		subject: string;
		type: "quiz" | "overview";
	} | null>(null);
	const [selectedTimeFrame, setSelectedTimeFrame] =
		useState<TimeFrame>("twentyFourHours");
	const [currentPage, setCurrentPage] = useState(1);
	const [selectedQuizForAnalytics, setSelectedQuizForAnalytics] = useState<
		string | null
	>(null);
	const [showTestModal, setShowTestModal] = useState(false);
	const [tests, setTests] = useState<MockTest[]>([]);
	const [loadingTests, setLoadingTests] = useState(false);
	const [testsError, setTestsError] = useState<string | null>(null);
	const [selectedPrep, setSelectedPrep] = useState<string | null>(null);

	// Fetch analytics data from API
	const {
		data: analyticsData,
		isLoading,
		error: analyticsError,
	} = useGetAnalytics(selectedPrep ?? undefined);
	const analytics = analyticsData?.data?.data?.analytics;

	// Fetch quiz data for tests taken section
	const {
		data: quizzesData,
		isLoading: quizzesLoading,
		error: quizzesError,
	} = useGetAllQuizzes(currentPage, 10);

	// Fetch quiz analytics when a quiz is selected
	const {
		data: quizAnalyticsData,
		isLoading: quizAnalyticsLoading,
		error: quizAnalyticsError,
	} = useGetQuizAnalytics(selectedQuizForAnalytics || "");

	// Get current time frame data
	const getCurrentTimeFrameData = (): TimeFrameData | undefined => {
		const timeFrameMap: Record<TimeFrame, any> = {
			twentyFourHours: analytics?.twentyFourHours,
			sevenDays: analytics?.sevenDays,
			thirtyDays: analytics?.thirtyDays,
			overall: analytics?.overall,
		};
		return timeFrameMap[selectedTimeFrame];
	};

	const currentData = getCurrentTimeFrameData();

	// Calculate analytics data based on API data
	const totalMCQsSolved = currentData?.mcqsSolvedCount || 0;
	const correctAnswers = currentData?.correctAnswers || 0;
	const wrongAnswers = currentData?.wrongAnswers || 0;

	// Check if error is specifically 404 with no analytics message
	const isNoAnalyticsError =
		analyticsError &&
		(analyticsError as any)?.response?.status === 404 &&
		(analyticsError as any)?.response?.data?.message?.includes(
			"No analytics found for this user"
		);

	// Determine if we should show empty state
	const hasAnalyticsData = !isLoading && !analyticsError && totalMCQsSolved > 0;

	// Map selected testId (or API quiz_name) to human-friendly name for banner
	const selectedTestName = tests.find(
		(t) => t.testId === (selectedPrep ?? "")
	)?.name;
	const apiQuizId = analytics?.quiz_name as string | undefined;
	const apiQuizFriendlyName = tests.find(
		(t) => t.testId === (apiQuizId ?? "")
	)?.name;
	const bannerValue = selectedTestName ?? apiQuizFriendlyName ?? "Select Test";
	const aiAnalytic =
		analytics?.ai_based_analytic ||
		"You need to give some more quizzes in order to get helpful AI insights";

	// Load available tests for Prep selection
	React.useEffect(() => {
		let cancelled = false;
		(async () => {
			try {
				setLoadingTests(true);
				setTestsError(null);
				const res = await getMockTests();
				const list = (res.data?.data?.mockTests ?? []) as MockTest[];
				if (cancelled) return;
				setTests(list);
				if (!selectedPrep) {
					const first = list[0];
					if (first) setSelectedPrep(first.testId);
				}
			} catch (e: any) {
				if (!cancelled)
					setTestsError(e?.response?.data?.message || "Failed to load tests.");
			} finally {
				if (!cancelled) setLoadingTests(false);
			}
		})();
		return () => {
			cancelled = true;
		};
	}, []);

	// If API returns a quiz_name, sync it to selectedPrep (but don't fight user interaction)
	React.useEffect(() => {
		const apiQuiz = analytics?.quiz_name;
		if (apiQuiz && !selectedPrep) setSelectedPrep(apiQuiz);
	}, [analytics?.quiz_name]);
	const quizzes = quizzesData?.quizzes || [];

	// Format quiz data for table display
	const formatQuizDate = (dateString: string) => {
		const date = new Date(dateString);
		return date
			.toLocaleDateString("en-GB", {
				day: "2-digit",
				month: "2-digit",
				year: "2-digit",
				hour: "2-digit",
				minute: "2-digit",
			})
			.replace(",", "");
	};

	const formatQuizData = (quiz: any) => ({
		id: quiz._id,
		testName: quiz.entryTest || "Test",
		date: formatQuizDate(quiz.createdAt),
		totalMcqs: quiz.totalMcqs,
		correctMcqs: quiz.correctMcqs,
		mcqsSolved: quiz.totalMcqs,
		correctAnswers: quiz.correctMcqs,
		wrongAnswers: quiz.totalMcqs - quiz.correctMcqs,
	});

	const formattedQuizzes = quizzes.map(formatQuizData);

	// Transform quiz pagination data to match ResourcePagination component format
	const transformQuizPagination = (quizPagination: any) => {
		if (!quizPagination) return null;

		return {
			totalItems: quizPagination.totalItems,
			totalPages: quizPagination.totalPages,
			currentPage: quizPagination.currentPage,
			pageSize: quizPagination.limit,
			hasNext: quizPagination.hasNextPage,
			hasPrev: quizPagination.hasPrevPage,
		};
	};

	const paginationInfo = transformQuizPagination(quizzesData?.pagination);

	// Get AI topic analytics for subject feedback
	const subjectFeedback = Object.entries(
		analytics?.ai_topic_analytic || {}
	).map(([subject, feedback]) => ({
		subject,
		aiRecommendation: feedback,
		// weakTopics: [], // This would need to be extracted from the feedback or provided separately
		// aiPrediction: "Continue practicing to improve your performance!",
	}));

	// Analytics data using real API data where possible
	const totalAttemptedData = [
		{
			name: "Correct Answers",
			value: correctAnswers,
			fill: "rgba(89, 54, 205, 1)",
		},
		{
			name: "Wrong Answers",
			value: wrongAnswers,
			fill: "rgba(89, 54, 205, 0.5)",
		},
	];

	// Transform analytics data using utility functions
	const mcqTypeData = transformDataToAnalytics(
		currentData?.mcqsType || {},
		currentData?.subject_division || {},
		"type"
	);

	const difficultyTypeData = transformDataToAnalytics(
		currentData?.difficultyType || {},
		currentData?.subject_division || {},
		"difficulty"
	);

	const subjectWiseData = Object.entries(currentData?.subjects || {}).map(
		([subject, value]) => {
			const subjectData = currentData?.subject_division?.[subject];
			const subjectCorrect = Object.values(
				subjectData?.difficulty || {}
			).reduce((sum: number, data: any) => sum + (data.correct || 0), 0);
			const subjectWrong = Object.values(subjectData?.difficulty || {}).reduce(
				(sum: number, data: any) => sum + (data.wrong || 0),
				0
			);

			return {
				name: subject,
				total: value as number,
				correct: { value: subjectCorrect, fill: CHART_COLORS.correct },
				wrong: { value: subjectWrong, fill: CHART_COLORS.wrong },
			};
		}
	);

	// To uncomment for Future Predictions

	// // Subject-wise data for progress bars
	// const _subjectWiseData = Object.entries(currentData?.subjects || {}).map(
	// 	([subject, value]) => ({
	// 		name: subject,
	// 		totalValue: 100,
	// 		lightValue:
	// 			totalMCQsSolved > 0 ? ((value as number) * 100) / totalMCQsSolved : 0,
	// 	})
	// );

	// const isFuturePredictionComingSoon = false;

	// // Future predictions with slight improvement over current performance
	// const predictedCorrectAnswers = Math.round(correctAnswers * 1.1); // 10% improvement prediction
	// const predictedWrongAnswers = Math.round(wrongAnswers * 0.9); // 10% reduction in wrong answers

	// const futurePredictionData = [
	// 	{
	// 		name: "Correct Answers",
	// 		value: predictedCorrectAnswers,
	// 		fill: "rgba(89, 54, 205, 1)",
	// 	},
	// 	{
	// 		name: "Wrong Answers",
	// 		value: predictedWrongAnswers,
	// 		fill: "rgba(89, 54, 205, 0.5)",
	// 	},
	// ];

	// const subjectWisePredictionData = _subjectWiseData.map((item) => ({
	// 	name: item.name,
	// 	lightValue: Math.min(Math.round(item.lightValue * 1.1), 95), // 10% improvement, capped at 95%
	// 	totalValue: 100,
	// }));

	//


	const handleGoToTest = () => {
		navigate({ to: `/quiz/${selectedQuizForAnalytics}` });
	};

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}
			<main className="container mx-auto p-4 pb-20 lg:pb-4">
				<PrepBanner
					className="mb-7"
					value={bannerValue}
					maxPillWidth={360}
					onChangeClick={() => setShowTestModal(true)}
				/>

				{/* Header */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">Analytics</h2>
				</div>

				{/* AI Based Analytics Section */}
				{hasAnalyticsData && (
					<div>
						<h3 className="text-xl font-bold text-gray-800 mb-7 flex items-center gap-[5px] justify-center">
							<img src={ICONS.magicstar} />
							AI Based Analytics
						</h3>
						<div className="mb-9 p-0.5 rounded-3xl bg-gradient-to-r from-[#EF43FF] to-[#5936CD]">
							{/* <div className="bg-background rounded-3xl"> */}
							<p
								className="bg-background rounded-3xl p-5 text-[#475569] text-lg md:text-xl font-semibold"
								dangerouslySetInnerHTML={{ __html: aiAnalytic }}
							/>
							{/* </div> */}
						</div>
					</div>
				)}

				{/* Overview Header */}
				<div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">Overview</h2>
				</div>

				{/* Analytics with Time Frame Tabs */}
				{isLoading ? (
					<AnalyticsMainLoading />
				) : isNoAnalyticsError ? (
					<AnalyticsEmptyState variant="no-tests-completed" />
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" />
				) : (
					<div className="mb-9 px-6 py-7 bg-white rounded-3xl border border-gray-300">
						{/* Time Frame Tabs */}
						<div className="mb-6 flex gap-2 justify-center">
							{TIME_FRAME_OPTIONS.map(({ key, label }) => (
								<Button
									key={key}
									onClick={() => setSelectedTimeFrame(key)}
									className={`px-3 py-2 h-8 md:px-4 md:h-10 rounded-full text-xs md:text-sm font-medium ${
										selectedTimeFrame === key
											? "bg-accent text-white"
											: "bg-gray-100 text-gray-600 hover:bg-gray-200"
									}`}
								>
									{label}
								</Button>
							))}
						</div>

						{/* Header */}
						<div className="mb-5">
							<h3 className="text-sm font-bold text-gray-400 mb-2.5">Report</h3>
							<p className="text-[32px] font-medium text-gray-700">
								{totalMCQsSolved}{" "}
								<span className="text-base font-medium text-gray-400">
									total
								</span>
							</p>
						</div>

						{/* Main Content Grid */}
						<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between items-end">
							<PieChart
								data={totalAttemptedData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="flex flex-col justify-between h-[90%]"
							/>
							<AnalyticsSection
								title="Subject Wise"
								data={subjectWiseData}
								onItemClick={(item) =>
									setSelectedSubjectAndType({
										type: "overview",
										subject: item.name,
									})
								}
								enableShowTotalCta={true}
							/>
							<AnalyticsSection
								title="MCQ's Type"
								data={mcqTypeData}
								borderColor="border-[#D2D5DA]"
							/>
							<AnalyticsSection
								title="Difficulty Type"
								data={difficultyTypeData}
							/>
						</div>
					</div>
				)}

				{/* Tests Taken Section */}
				{!selectedQuizForAnalytics ? (
					<div className="mb-9">
						<div className="mb-7">
							<h2 className="font-bold text-2xl text-[#202224]">Tests Taken</h2>
						</div>

						<div className="bg-white rounded-3xl border border-gray-300 overflow-hidden">
							{/* Mobile: Horizontal scroll wrapper */}
							<div className="overflow-x-auto">
								{/* Table Header */}
								<div
									className="grid gap-4 px-6 py-4 bg-gray-50 border-b border-gray-200 text-sm font-semibold text-gray-600 min-w-[600px]"
									style={{
										gridTemplateColumns: "60px 1.7fr 1.5fr 1fr 1fr 80px",
									}}
								>
									<div>Sr</div>
									<div>Test Name</div>
									<div>Date</div>
									<div>Total MCQs</div>
									<div>Correct MCQs</div>
									<div></div>
								</div>

								{/* Table Rows */}
								<div className="divide-y divide-gray-100">
									{quizzesLoading ? (
										<div className="px-6 py-8 text-center text-gray-500">
											Loading tests...
										</div>
									) : quizzesError ? (
										<div className="px-6 py-8 text-center text-red-500">
											Error loading tests
										</div>
									) : formattedQuizzes.length === 0 ? (
										<div className="px-6 py-8 text-center text-gray-500">
											No tests taken yet
										</div>
									) : (
										formattedQuizzes.map((test, index) => (
											<div
												key={test.id}
												className="grid gap-4 px-6 py-4 hover:bg-gray-50 transition-colors duration-200 min-w-[600px]"
												style={{
													gridTemplateColumns: "60px 1.7fr 1.5fr 1fr 1fr 80px",
												}}
											>
												<div className="text-sm text-gray-600">
													{String(index + 1).padStart(2, "0")}
												</div>
												<div>
													<button className="text-accent text-left hover:underline text-sm font-medium">
														{test.testName}
													</button>
												</div>
												<div className="text-sm text-gray-600">{test.date}</div>
												<div className="text-sm text-gray-900 font-medium">
													{test.totalMcqs}
												</div>
												<div className="text-sm text-gray-900 font-medium">
													{test.correctMcqs}
												</div>
												<div>
													<button
														onClick={() => setSelectedQuizForAnalytics(test.id)}
														className="text-accent text-left hover:underline text-sm"
													>
														Analytics
													</button>
												</div>
											</div>
										))
									)}
								</div>
							</div>

							{/* Pagination */}
							{paginationInfo && paginationInfo.totalPages > 1 && (
								<div className="mt-6 flex justify-center">
									<ResourcePagination
										pagination={paginationInfo}
										onPageChange={setCurrentPage}
									/>
								</div>
							)}
						</div>
					</div>
				) : (
					/* Test Detail View */
					<div className="mb-9">
						<div className="mb-7">
							<h2 className="font-bold text-2xl text-[#202224]">Tests Taken</h2>
						</div>

						<div className="px-6 py-7 bg-white rounded-3xl border border-gray-300">
							{/* Header with Back Button */}
							<div className="mb-5 flex items-center justify-between">
								<div className="flex items-center gap-4">
									<button
										onClick={() => setSelectedQuizForAnalytics(null)}
										className="flex items-center gap-2 text-accent transition-colors"
									>
										<svg
											className="w-10 h-10"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={2}
												d="M15 19l-7-7 7-7"
											/>
										</svg>
									</button>
									<div>
										<h3 className="text-sm font-bold text-gray-400 mb-2.5">
											Report
										</h3>
										{quizAnalyticsLoading ? (
											<p className="text-[32px] font-medium text-gray-700">
												Loading...
											</p>
										) : quizAnalyticsError ? (
											<p className="text-[32px] font-medium text-red-500">
												Error loading data
											</p>
										) : quizAnalyticsData ? (
											<p className="text-[32px] font-medium text-gray-700">
												{quizAnalyticsData.analytics.totalMcqs}{" "}
												<span className="text-base font-medium text-gray-400">
													total
												</span>
											</p>
										) : (
											<p className="text-[32px] font-medium text-gray-700">
												No data
											</p>
										)}
									</div>
								</div>
								<button className="text-accent text-xl underline underline-offset-[4px] hover:no-underline transition-all duration-200" onClick={handleGoToTest}>
									Go to test
								</button>
							</div>

							{/* Main Content Grid */}
							{quizAnalyticsLoading ? (
								<div className="text-center py-8">Loading analytics...</div>
							) : quizAnalyticsError ? (
								<div className="text-center py-8 text-red-500">
									Error loading analytics
								</div>
							) : quizAnalyticsData ? (
								<div className="grid grid-cols-1 lg:grid-cols-4 gap-9 lg:justify-between items-end">
									<PieChart
										data={[
											{
												name: "Correct Answers",
												value: quizAnalyticsData.analytics.correctAnswers,
												fill: CHART_COLORS.correct,
											},
											{
												name: "Wrong Answers",
												value: quizAnalyticsData.analytics.wrongAnswers,
												fill: CHART_COLORS.wrong,
											},
										]}
										showLegend={true}
										innerRadius={0}
										outerRadius={70}
										className="flex flex-col justify-between h-[90%]"
									/>
									<AnalyticsSection
										title="Subject Wise"
										data={transformSubjectData(
											extractSubjectsFromDivision(
												quizAnalyticsData.analytics.subjectDivision
											)
										)}
										onItemClick={(item) =>
											setSelectedSubjectAndType({
												type: "quiz",
												subject: item.name,
											})
										}
										enableShowTotalCta={true}
									/>
									<AnalyticsSection
										title="MCQ's Type"
										data={transformSubjectData(
											extractMcqsTypeFromDivision(
												quizAnalyticsData.analytics.subjectDivision
											)
										)}
										borderColor="border-[#D2D5DA]"
									/>
									<AnalyticsSection
										title="Difficulty Type"
										data={transformSubjectData(
											extractDifficultyTypeFromDivision(
												quizAnalyticsData.analytics.subjectDivision
											)
										)}
									/>
								</div>
							) : (
								<div className="text-center py-8 text-gray-500">
									No analytics data available
								</div>
							)}
						</div>
					</div>
				)}

				{/* Subject Wise Feedback Section */}
				<div className="mb-9">
					<div className="mb-7">
						<h2 className="font-bold text-2xl text-[#202224]">
							Subject Wise Feedback
						</h2>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
						{subjectFeedback.length === 0 ? (
							<div className="col-span-full text-center py-8 text-gray-500">
								No subject feedback available yet
							</div>
						) : (
							subjectFeedback.map((feedback, index) => (
								<div
									key={index}
									className="bg-white rounded-3xl border border-gray-300 p-6"
								>
									{/* Subject Header */}
									<div className="flex items-center gap-3 mb-4">
										<div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
											<div className="w-6 h-6 bg-gray-300 rounded-full"></div>
										</div>
										<h3 className="font-semibold text-lg text-gray-800">
											{feedback.subject}
										</h3>
									</div>

									{/* AI-Based Recommendation */}
									<div className="mb-4">
										<h4 className="text-sm font-semibold text-gray-600 mb-2">
											AI-Based Recommendation:
										</h4>
										<div
											className="text-sm text-accent font-bold leading-relaxed"
											dangerouslySetInnerHTML={{
												__html: feedback.aiRecommendation,
											}}
										/>
									</div>

									{/* Weak Topics */}
									{/* <div className="mb-4">
										<h4 className="text-sm font-semibold text-gray-600 mb-2">
											Weak Topics:
										</h4>
										<ul className="space-y-1">
											{feedback.weakTopics.map((topic, topicIndex) => (
												<li
													key={topicIndex}
													className="text-sm text-gray-700 flex items-start gap-2"
												>
													<span className="text-gray-400">•</span>
													<span>{topic}</span>
												</li>
											))}
										</ul>
									</div> */}

									{/* AI-Based Prediction */}
									{/* <div>
										<h4 className="text-sm font-semibold text-gray-600 mb-2">
											AI-Based Prediction:
										</h4>
										<p className="text-sm text-accent leading-relaxed">
											"{feedback.aiPrediction}"
										</p>
									</div> */}
								</div>
							))
						)}
					</div>
				</div>

				{/* To uncomment for Future Predictions */}

				{/* Future Predictions Heading (Always Visible) */}
				{/* <div className="mb-7">
					<h2 className="font-bold text-2xl text-[#202224]">
						Future Predictions
					</h2>
				</div> */}

				{/* Future Predictions */}
				{/* {isFuturePredictionComingSoon ? (
					<FuturePredictionComingSoon />
				) : isLoading ? (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						<AnalyticsFutureLoading type="left" />
						<AnalyticsFutureLoading type="right" />
					</div>
				) : analyticsError ? (
					<AnalyticsEmptyState variant="no-data" className="py-20" />
				) : !hasAnalyticsData ? (
					<AnalyticsEmptyState variant="no-tests-completed" layout="grid" />
				) : (
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-2.5">
						<AnalyticsCard
							title="PREDICTED MCQ'S"
							value={predictedCorrectAnswers + predictedWrongAnswers}
							subtitle="predicted"
							className="flex flex-col"
						>
							<PieChart
								data={futurePredictionData}
								showLegend={true}
								innerRadius={0}
								outerRadius={70}
								className="h-full flex flex-col justify-between"
							/>
						</AnalyticsCard>

						<AnalyticsCard
							title="SUBJECT WISE PREDICTION"
							value={`${Math.round((correctAnswers / (correctAnswers + wrongAnswers)) * 110) || 75}%`}
							subtitle="predicted avg"
						>
							<div className="space-y-4">
								{subjectWisePredictionData.map((item, index) => (
									<div key={index} className="space-y-2">
										<div className="flex justify-between items-center">
											<span className="text-sm font-medium text-[#1A1C1E]">
												{formatName(item.name)}
											</span>
										</div>
										<div className="flex gap-0.5">
											<div
												className="h-4 bg-[#5936CD80] transition-all duration-300"
												style={{ width: `${item.lightValue}%` }}
											/>
											<div
												className="h-4 bg-accent transition-all duration-300"
												style={{
													width: `${item.totalValue - item.lightValue}%`,
												}}
											/>
										</div>
									</div>
								))}
							</div>
						</AnalyticsCard>
					</div>
				)} */}

				{/*  */}

				{/* Subject Detail Modal for Overview Analytics */}
				{selectedSubjectAndType &&
					selectedSubjectAndType.type === "overview" &&
					currentData?.subject_division?.[selectedSubjectAndType?.subject] && (
						<SubjectDetailModal
							isOpen={!!selectedSubjectAndType?.subject}
							onClose={() => setSelectedSubjectAndType(null)}
							subjectName={selectedSubjectAndType?.subject}
							subjectData={
								currentData.subject_division[selectedSubjectAndType?.subject]
							}
						/>
					)}

				{/* Subject Detail Modal for Quiz Analytics */}
				{selectedSubjectAndType &&
					selectedSubjectAndType.type === "quiz" &&
					selectedQuizForAnalytics &&
					quizAnalyticsData?.analytics?.subjectDivision?.[
						selectedSubjectAndType.subject
					] && (
						<SubjectDetailModal
							isOpen={!!selectedSubjectAndType.subject}
							onClose={() => setSelectedSubjectAndType(null)}
							subjectName={selectedSubjectAndType.subject}
							subjectData={
								quizAnalyticsData.analytics.subjectDivision[
									selectedSubjectAndType.subject
								]
							}
							isQuizAnalytics={true}
						/>
					)}
				{/* Test Selection Modal */}
				{showTestModal && (
					<TestSelectionModal
						open={showTestModal}
						tests={tests}
						selectedPrep={selectedPrep || ""}
						onClose={() => setShowTestModal(false)}
						onSelect={(testId) => {
							setSelectedPrep(testId);
							setShowTestModal(false);
						}}
						loading={loadingTests}
						error={testsError}
					/>
				)}
			</main>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/analytics")({
	beforeLoad: () => {},
	component: Page,
});
