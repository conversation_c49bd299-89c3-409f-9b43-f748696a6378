import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import { Navbar } from "@/components/layout/main/navbar";
import { useMediaQuery } from "react-responsive";
import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useGetAllQuizzes } from "@/lib/queries/tests.query";
import { useState } from "react";
import ResourcePagination from "@/components/ui/resource-pagination";

const AttemptsPage = () => {
	const isDesktop = useMediaQuery({ minWidth: 1024 });
	const navigate = useNavigate();
	const [currentPage, setCurrentPage] = useState(1);

	// Fetch quiz data for tests taken section
	const {
		data: quizzesData,
		isLoading: quizzesLoading,
		error: quizzesError,
	} = useGetAllQuizzes(currentPage, 10);

	const quizzes = quizzesData?.quizzes || [];

	// Format quiz date
	const formatQuizDate = (dateString: string) => {
		const date = new Date(dateString);
		return date.toLocaleDateString("en-US", {
			year: "numeric",
			month: "short",
			day: "numeric",
		});
	};

	const formatQuizData = (quiz: any) => ({
		id: quiz._id,
		testName: quiz.entryTest || "Test",
		type: quiz.type || "Unknown",
		date: formatQuizDate(quiz.createdAt),
		totalMcqs: quiz.totalMcqs,
		correctMcqs: quiz.correctMcqs,
		mcqsSolved: quiz.totalMcqs,
		correctAnswers: quiz.correctMcqs,
		wrongAnswers: quiz.totalMcqs - quiz.correctMcqs,
	});

	const formattedQuizzes = quizzes.map(formatQuizData);

	// Transform quiz pagination data to match ResourcePagination component format
	const transformQuizPagination = (quizPagination: any) => {
		if (!quizPagination) return null;

		return {
			totalItems: quizPagination.totalItems,
			currentPage: quizPagination.currentPage,
			totalPages: quizPagination.totalPages,
			hasNextPage: quizPagination.hasNextPage,
			hasPrevPage: quizPagination.hasPrevPage,
		};
	};

	const paginationInfo = transformQuizPagination(quizzesData?.pagination);

	const handleQuizAnalytics = (quizId: string) => {
		navigate({ to: `/quiz/${quizId}` });
	};

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}

			<div className="container mx-auto p-6 pb-20">
				{/* Header */}
				<div className="mb-7">
					<h1 className="font-bold text-2xl text-[#202224]">Test Attempts</h1>
					<p className="text-gray-600 mt-2">View all your attempted tests and their results</p>
				</div>

				{/* Tests Taken Table */}
				<div className="bg-white rounded-3xl border border-gray-300 overflow-hidden">
					{/* Mobile: Horizontal scroll wrapper */}
					<div className="overflow-x-auto">
						{/* Table Header */}
						<div
							className="grid gap-4 px-6 py-4 bg-gray-50 border-b border-gray-200 text-sm font-semibold text-gray-600 min-w-[700px]"
							style={{
								gridTemplateColumns: "60px 1.5fr 1fr 1.2fr 1fr 1fr 80px",
							}}
						>
							<div>Sr</div>
							<div>Test Name</div>
							<div>Type</div>
							<div>Date</div>
							<div>Total MCQs</div>
							<div>Correct MCQs</div>
							<div></div>
						</div>

						{/* Table Rows */}
						<div className="divide-y divide-gray-100">
							{quizzesLoading ? (
								<div className="px-6 py-8 text-center text-gray-500">
									Loading tests...
								</div>
							) : quizzesError ? (
								<div className="px-6 py-8 text-center text-red-500">
									Error loading tests
								</div>
							) : formattedQuizzes.length === 0 ? (
								<div className="px-6 py-8 text-center text-gray-500">
									No tests attempted yet
								</div>
							) : (
								formattedQuizzes.map((test, index) => (
									<div
										key={test.id}
										className="grid gap-4 px-6 py-4 hover:bg-gray-50 transition-colors duration-200 min-w-[700px]"
										style={{
											gridTemplateColumns: "60px 1.5fr 1fr 1.2fr 1fr 1fr 80px",
										}}
									>
										<div className="text-sm text-gray-600">
											{String((currentPage - 1) * 10 + index + 1).padStart(2, "0")}
										</div>
										<div>
											<button 
												onClick={() => handleQuizAnalytics(test.id)}
												className="text-accent text-left hover:underline text-sm font-medium"
											>
												{test.testName}
											</button>
										</div>
										<div className="text-sm text-gray-600 capitalize">
											{test.type}
										</div>
										<div className="text-sm text-gray-600">{test.date}</div>
										<div className="text-sm text-gray-900 font-medium">
											{test.totalMcqs}
										</div>
										<div className="text-sm text-gray-900 font-medium">
											{test.correctMcqs}
										</div>
										<div>
											<button
												onClick={() => handleQuizAnalytics(test.id)}
												className="text-accent text-left hover:underline text-sm"
											>
												View
											</button>
										</div>
									</div>
								))
							)}
						</div>
					</div>

					{/* Pagination */}
					{paginationInfo && paginationInfo.totalPages > 1 && (
						<div className="px-6 py-4 border-t border-gray-200">
							<ResourcePagination
								currentPage={currentPage}
								totalPages={paginationInfo.totalPages}
								onPageChange={setCurrentPage}
								hasNextPage={paginationInfo.hasNextPage}
								hasPrevPage={paginationInfo.hasPrevPage}
							/>
						</div>
					)}
				</div>
			</div>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/attempts")({
	component: AttemptsPage,
});
