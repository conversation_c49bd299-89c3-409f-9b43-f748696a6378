import React from "react";
import {
	createFile<PERSON>out<PERSON>,
	useCanG<PERSON><PERSON><PERSON>,
	useNavigate,
	useRouter,
} from "@tanstack/react-router";
import { useState, useEffect } from "react";
import { useMediaQuery } from "react-responsive";
import TestLayout from "@/components/layout/mcqs/test-layout";
import MCQMobileLayout from "@/components/layout/mcqs/mcq-mobile-layout";
import { Test, MCQ } from "@/features/mcqs/types";
import { useGetQuiz } from "@/lib/queries/tests.query";
// import { useToast } from "@/hooks/use-toast";
import { But<PERSON> } from "@/components/ui/button";
import { BookOpen, ArrowLeft } from "react-feather";
import { QuizMCQ, QuizResponse } from "@/features/tests/types";
import { getBookmarksList } from "@/features/bookmarks/services";

// Transform API quiz data to the format expected by the MCQ components
const transformQuizToTest = (apiData: QuizResponse): Test => {
	const mcqs: MCQ[] = apiData.mcqs.map((mcq: QuizMCQ) => ({
		id: mcq._id,
		question: mcq.title,
		options: mcq.options,
		correctAnswer: mcq.answer, // Keep as undefined if not provided
		subject: mcq.subject as any,
		tag: mcq.topic,
		difficulty: mcq.difficulty,
		description: mcq.explanation,
	}));

	const formatDuration = (totalTimeInSeconds: number) => {
		const hours = Math.floor(totalTimeInSeconds / 3600);
		const minutes = Math.floor((totalTimeInSeconds % 3600) / 60);
		const seconds = totalTimeInSeconds % 60;
		return `${hours.toString().padStart(2, "0")}:${minutes
			.toString()
			.padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
	};

	const formattedDuration = formatDuration(apiData.totalTime);

	return {
		id: apiData._id,
		title: `${apiData.entryTest} ${apiData.type === `mock` ? `Mock` : `Custom`} Test`,
		totalQuestions: apiData.mcqs.length,
		duration: formattedDuration,
		liveCheck: false,
		mcqs: mcqs,
	};
};

type QuizNotFoundComponentProps = { goBack: () => void };

// Static component to show when quiz is not found or loading fails
const QuizNotFoundComponent: React.FC<QuizNotFoundComponentProps> = ({
	goBack,
}) => {
	return (
		<div className="flex flex-col items-center justify-center min-h-screen bg-gray-50 p-4">
			<div className="text-center max-w-md">
				<BookOpen className="w-16 h-16 text-gray-400 mx-auto mb-4" />
				<h2 className="text-2xl font-bold text-gray-900 mb-2">
					Quiz Not Found
				</h2>
				<p className="text-gray-600 mb-6">
					The quiz you're looking for doesn't exist or has been removed.
				</p>
				<Button
					onClick={goBack}
					className="bg-purple-600 hover:bg-purple-700 text-white flex items-center gap-2"
				>
					<ArrowLeft className="w-4 h-4" />
					Back to Saved
				</Button>
			</div>
		</div>
	);
};

const SavedQuizPage = () => {
	const { quizId } = Route.useParams();
	const navigate = useNavigate();
	const router = useRouter();
	const canGoBack = useCanGoBack();
	// const { toast } = useToast();
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	// State for MCQ functionality
	const [selectedAnswers, setSelectedAnswers] = useState<
		Record<string, number>
	>({});
	const [answerStatus, setAnswerStatus] = useState<Record<string, boolean>>({});
	const [showResults, setShowResults] = useState(false);
	const [liveCheckEnabled, setLiveCheckEnabled] = useState(false);
	const [testTimesUp, setTestTimesUp] = useState(false);
	const [bookmarkList, setBookmarkList] = useState({});
	const [refetchBookmarks, setRefetchBookmarks] = useState(false);

	// Fetch quiz data
	const {
		data: quizData,
		isLoading: quizLoading,
		error: quizError,
	} = useGetQuiz(quizId);

	// Function to fetch bookmarks for the bookmark functionality
	const fetchBookmarks = async () => {
		const [quizResponse, mcqResponse] = await Promise.all([
			getBookmarksList({
				category: "quiz",
				page: 1,
				limit: 100,
			}),
			getBookmarksList({
				category: "mcq",
				page: 1,
				limit: 100,
			}),
		]);
		setBookmarkList({
			quiz: quizResponse?.data?.data,
			mcq: mcqResponse?.data?.data,
		});
	};

	useEffect(() => {
		fetchBookmarks();
	}, [refetchBookmarks]);

	// Initialize selected answers from saved quiz data
	useEffect(() => {
		if (quizData && quizData.mcqs) {
			const initialAnswers: Record<string, number> = {};
			const initialStatus: Record<string, boolean> = {};

			quizData.mcqs.forEach((mcq: QuizMCQ) => {
				if (mcq.chosenOption !== null && mcq.chosenOption !== undefined) {
					initialAnswers[mcq._id] = mcq.chosenOption;
					// Always show status for saved quizzes since they have answers
					if (mcq.answer !== undefined) {
						initialStatus[mcq._id] = mcq.chosenOption === mcq.answer;
					}
				}
			});

			setSelectedAnswers(initialAnswers);
			setAnswerStatus(initialStatus);
			if (quizData.completed) setShowResults(true);
		}
	}, [quizData]);

	// Handle back navigation
	const handleBackClick = () => {
		// Use browser history to go back, fallback to saved page if no history
		if (canGoBack) {
			router.history.back();
		} else {
			navigate({ to: "/saved" });
		}
	};

	const handleAnswerSelect = (questionId: string, optionIndex: number) => {
		if (showResults) return; // Don't allow changes if results are shown

		setSelectedAnswers((prev) => ({
			...prev,
			[questionId]: optionIndex,
		}));

		if (liveCheckEnabled && quizData) {
			const mcq = quizData.mcqs.find((m: QuizMCQ) => m._id === questionId);
			if (mcq && mcq.answer !== undefined) {
				setAnswerStatus((prev) => ({
					...prev,
					[questionId]: optionIndex === mcq.answer,
				}));
			}
		}
	};

	const handleCheckAnswers = () => {
		// if (!quizData) return;
		// const newAnswerStatus: Record<string, boolean> = {};
		// quizData.mcqs.forEach((mcq: QuizMCQ) => {
		// 	if (mcq.answer !== undefined) {
		// 		const selectedAnswer = selectedAnswers[mcq._id];
		// 		newAnswerStatus[mcq._id] = selectedAnswer === mcq.answer;
		// 	}
		// });
		// setAnswerStatus(newAnswerStatus);
		// setShowResults(true);
		// toast({
		// 	title: "Results Shown",
		// 	description: "Quiz results are now displayed.",
		// });
	};

	// Handle loading state
	if (quizLoading) {
		return (
			<div className="flex items-center justify-center min-h-screen">
				<div className="text-center">
					<div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-600 mx-auto mb-4"></div>
					<p className="text-gray-600">Loading quiz...</p>
				</div>
			</div>
		);
	}

	// Handle error or no data
	if (quizError || !quizData) {
		return <QuizNotFoundComponent goBack={handleBackClick} />;
	}

	// Transform quiz data to test format
	const test = transformQuizToTest(quizData);

	return isDesktop ? (
		<TestLayout
			test={test}
			liveCheckEnabled={liveCheckEnabled}
			setLiveCheckEnabled={setLiveCheckEnabled}
			selectedAnswers={selectedAnswers}
			answerStatus={answerStatus}
			showResults={showResults}
			onAnswerSelect={handleAnswerSelect}
			onCheckAnswers={handleCheckAnswers}
			testTimesUp={testTimesUp}
			setTestTimesUp={setTestTimesUp}
			bookmarkList={bookmarkList}
			setRefetchBookmarks={setRefetchBookmarks}
			isSingleQuiz={true}
			onBackClick={handleBackClick}
		/>
	) : (
		<MCQMobileLayout
			test={test}
			liveCheckEnabled={liveCheckEnabled}
			setLiveCheckEnabled={setLiveCheckEnabled}
			selectedAnswers={selectedAnswers}
			showResults={showResults}
			onAnswerSelect={handleAnswerSelect}
			onCheckAnswers={handleCheckAnswers}
			bookmarkList={bookmarkList}
			setRefetchBookmarks={setRefetchBookmarks}
			isSingleQuiz={true}
			onBackClick={handleBackClick}
		/>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/quiz/$quizId")({
	component: SavedQuizPage,
});
