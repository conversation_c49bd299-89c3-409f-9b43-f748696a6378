import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, useNavigate } from "@tanstack/react-router";
import { useEffect, useState } from "react";
import { useMediaQuery } from "react-responsive";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import {
	Bookmark,
	ChevronRight,
	AlertTriangle,
	ChevronDown,
	ChevronUp,
	Send,
} from "react-feather";

import { useGetBookmarks } from "@/lib/queries/tests.query";
import { useToast } from "@/hooks/use-toast";
import { Navbar } from "@/components/layout/main/navbar";
import MobileSwitcher from "@/components/layout/main/mobile-switcher";
import MobileHeader from "@/components/common/mobile-header";
import { QuizMCQ } from "@/features/tests/types";
import { deleteBookmark, addBookmark } from "@/features/bookmarks/services";
import { toggleMCQType } from "@/lib/utils";
import { useGrokKeyStatus, useMCQChatbot } from "@/lib/queries/chatbot.query";
import GrokKeyBanner from "@/components/chatbot/grok-key-banner";
import ReportMCQDialog from "@/components/layout/mcqs/report-mcq-dialog";
import Latex from "react-latex-next";
import "katex/dist/katex.min.css";

import { getAllQuizzes, QuizItem } from "@/features/tests/services";

const SavedTestCard = ({
	test,
	onRemove,
}: {
	test: QuizItem;
	onRemove: (testId: string) => void;
}) => {
	const navigate = useNavigate();
	const { toast } = useToast();
	const [isBookmarked, setIsBookmarked] = useState(test.bookmarked);

	const formatDate = (dateString: string) =>
		new Date(dateString).toLocaleDateString("en-GB", {
			day: "2-digit",
			month: "2-digit",
			year: "numeric",
		});

	const getTestTitle = () =>
		`${test.entryTest} ${test.type === "mock" ? "Mock" : "Custom"} Test`;

	const getLastScore = () =>
		!test.completed ? "Not completed" : `${test.correctMcqs}/${test.totalMcqs}`;

	const handleAttempt = () => navigate({ to: `/quiz/${test._id}` });

	const handleToggleBookmark = async () => {
		try {
			if (isBookmarked) {
				const res = await deleteBookmark({ category: "quiz", id: test._id });
				if (res?.data?.status === 200 && res?.data?.success) {
					toast({ title: "Success", description: "Test bookmark removed!" });
					setIsBookmarked(false);

					onRemove(test._id);
				}
			} else {
				const res = await addBookmark({ category: "quiz", id: test._id });
				if (res?.data?.status === 200 && res?.data?.success) {
					toast({ title: "Success", description: "Test bookmarked!" });
					setIsBookmarked(true);
				}
			}
		} catch {
			toast({
				title: "Error",
				description: "Failed to toggle test bookmark",
				variant: "destructive",
			});
		}
	};

	return (
		<Card className="w-full h-full rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
			<CardContent className="p-6 flex flex-col h-full justify-between gap-4">
				{/* Title + Bookmark */}
				<div className="flex items-center justify-between">
					<h3 className="text-base font-semibold text-gray-900 leading-tight">
						{getTestTitle()} {test.subtype}
					</h3>
					<Button
						variant="icon"
						className="[&_svg]:size-5 px-1"
						onClick={handleToggleBookmark}
						title={isBookmarked ? "Remove bookmark" : "Bookmark this test"}
					>
						<Bookmark
							className={`w-5 h-5 text-gray-600 ${
								isBookmarked ? "fill-purple-600" : ""
							}`}
						/>
					</Button>
				</div>

				{/* Scores & Date */}
				<div className="flex flex-wrap items-center gap-4 text-[14px] text-[#7f858c]">
					<div className="font-inter">
						Obtained marks:{" "}
						<span className="font-inter font-[500]">{getLastScore()}</span>
					</div>
					<div className="font-inter">
						Attempted: {formatDate(test.createdAt)}
					</div>
				</div>

				{/* Button */}
				<Button
					onClick={handleAttempt}
					className="w-full bg-[#7C3AED] hover:bg-[#6B21A8] text-white font-medium py-2.5 px-4 rounded-lg flex items-center justify-center gap-2 text-[15px]"
				>
					View
					<ChevronRight className="w-4 h-4" />
				</Button>
			</CardContent>
		</Card>
	);
};

const SavedMCQCard = ({
	mcq,
	numbering,
	onRemove,
}: {
	mcq: QuizMCQ;
	numbering: number;
	onRemove: (mcqId: string) => void;
}) => {
	const { toast } = useToast();
	const [isBookmarked, setIsBookmarked] = useState(true);
	const [showExpertAnswer, setShowExpertAnswer] = useState(false);
	const [isReportDialogOpen, setIsReportDialogOpen] = useState(false);
	const [showChatbot, setShowChatbot] = useState(false);
	const [chatbotQuestion, setChatbotQuestion] = useState("");
	const [chatbotResponse, setChatbotResponse] = useState("");

	const { data: grokKeyStatus } = useGrokKeyStatus();
	const chatbotMutation = useMCQChatbot();

	const difficultyStyles = {
		easy: "border-green-300 bg-green-100 text-green-800",
		medium: "border-yellow-300 bg-yellow-100 text-yellow-800",
		hard: "border-red-300 bg-red-100 text-red-800",
	} as const;

	const handleToggleBookmark = async () => {
		try {
			if (isBookmarked) {
				const res = await deleteBookmark({ category: "mcq", id: mcq._id });
				if (res?.data?.status === 200 && res?.data?.success) {
					toast({ title: "Success", description: "MCQ bookmark removed!" });
					setIsBookmarked(false);

					onRemove(mcq._id);
				}
			} else {
				const res = await addBookmark({ category: "mcq", id: mcq._id });
				if (res?.data?.status === 200 && res?.data?.success) {
					toast({ title: "Success", description: "MCQ bookmarked!" });
					setIsBookmarked(true);
				}
			}
		} catch {
			toast({
				title: "Error",
				description: isBookmarked
					? "Failed to remove the MCQ bookmark"
					: "Failed to bookmark the MCQ",
				variant: "destructive",
			});
		}
	};

	const handleChatbotSubmit = async () => {
		if (!chatbotQuestion.trim()) {
			toast({
				title: "Error",
				description: "Please enter a question",
				variant: "destructive",
			});
			return;
		}

		const correctAnswer =
			mcq.answer !== undefined
				? `${String.fromCharCode(65 + mcq.answer)}.${mcq.options[mcq.answer]}`
				: "Answer not available";

		const payload = {
			mcqid: mcq._id,
			mcqTitle: mcq.title,
			options: mcq.options.map(
				(option, index) => `${String.fromCharCode(65 + index)}.${option}`
			),
			userChoice: "No answer selected",
			correctAnswer,
			explanation: mcq.explanation || "",
			question: chatbotQuestion,
		};

		try {
			const response = await chatbotMutation.mutateAsync(payload);
			const content = response.data.result?.choices?.[0]?.message?.content;
			if (content) {
				setChatbotResponse(content);
				setChatbotQuestion("");
			} else {
				throw new Error("Invalid response format");
			}
		} catch (error: any) {
			toast({
				title: "Error",
				description:
					error?.response?.data?.message || "Failed to get chatbot response",
				variant: "destructive",
			});
		}
	};

	return (
		<Card className="w-full bg-white border border-gray-200 hover:shadow-md transition-shadow">
			<CardContent className="p-6">
				{/* Header */}
				<div className="flex items-center justify-between mb-4">
					<div className="flex items-center gap-2">
						<p className="text-gray-500 font-bold">QUESTION #{numbering + 1}</p>
						{mcq.type && (
							<span className="text-sm px-2 py-0.5 font-semibold border border-purple-300 bg-purple-100 text-accent rounded-full">
								{toggleMCQType(mcq.type)}
							</span>
						)}
						{mcq.difficulty && (
							<span
								className={`text-sm px-2 py-0.5 font-semibold rounded-full border ${
									difficultyStyles[
										mcq.difficulty as keyof typeof difficultyStyles
									] || ""
								}`}
							>
								{mcq.difficulty}
							</span>
						)}
					</div>
					<div className="flex items-center gap-4 mx-4">
						<Button
							variant="icon"
							className="[&_svg]:size-5 px-0"
							onClick={handleToggleBookmark}
							title={isBookmarked ? "Remove bookmark" : "Bookmark this MCQ"}
						>
							<Bookmark
								className={`w-5 h-5 text-gray-600 ${
									isBookmarked ? "fill-accent" : ""
								}`}
							/>
						</Button>
						<Button
							variant="icon"
							className="[&_svg]:size-5 px-0"
							onClick={() => setIsReportDialogOpen(true)}
							title="Report this MCQ"
						>
							<AlertTriangle className="w-5 h-5 text-gray-600 hover:text-red-500" />
						</Button>
					</div>
				</div>

				{/* Question */}
				<h3 className="mb-6 text-lg font-medium">
					<Latex>{mcq.title}</Latex>
				</h3>

				{/* Options */}
				<div className="space-y-2">
					<p className="font-bold text-gray-400 mb-4">OPTIONS</p>
					{mcq.options.map((option, index) => (
						<div
							key={mcq._id + option + index}
							className={`p-2 rounded ${
								mcq.answer !== undefined && index === mcq.answer
									? "bg-green-50 border border-green-200"
									: "bg-gray-50"
							}`}
						>
							<span
								className={`text-gray-400 ${
									mcq.answer !== undefined && index === mcq.answer
										? "font-bold text-green-600"
										: ""
								}`}
							>
								({String.fromCharCode(65 + index)})
							</span>
							<span
								className={`text-base font-medium ml-2 ${
									mcq.answer !== undefined && index === mcq.answer
										? "text-green-600"
										: ""
								}`}
							>
								<Latex>{option}</Latex>
							</span>
							{mcq.answer !== undefined && index === mcq.answer && (
								<span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-0.5 rounded">
									Correct Answer
								</span>
							)}
						</div>
					))}
				</div>

				{/* Expert Answer + Chatbot */}
				{mcq.answer !== undefined && (
					<div className="mt-4">
						<div className="flex flex-col item-start sm:flex-row sm:items-center sm:justify-end gap-4">
							<Button
								variant="link"
								onClick={() => setShowExpertAnswer(!showExpertAnswer)}
								className="justify-start text-gray-600 flex items-center gap-1 text-sm"
							>
								{showExpertAnswer ? "Hide" : "View"} expert answer
								{showExpertAnswer ? (
									<ChevronUp size={16} />
								) : (
									<ChevronDown size={16} />
								)}
							</Button>
						</div>

						{showExpertAnswer && (
							<div className="mt-2 p-4 bg-gray-50 rounded-md">
								{mcq.explanation && (
									<p className="text-sm">
										<Latex>{mcq.explanation}</Latex>
									</p>
								)}
							</div>
						)}

						{showExpertAnswer && (
							<div className="mt-4">
								<div className="mb-4">
									<Button
										variant="outline"
										onClick={() => setShowChatbot(!showChatbot)}
										className="flex items-center gap-2 text-purple-600 border-purple-200 hover:bg-purple-50"
									>
										<span className="text-lg">🤖</span>
										Ask Chatbot
									</Button>
								</div>

								{showChatbot && (
									<div className="space-y-4">
										{!chatbotResponse && (
											<GrokKeyBanner
												hasGrokKey={grokKeyStatus?.has_groq_api_key || false}
												className="mb-4"
											/>
										)}

										<div className="rounded-md">
											{chatbotResponse && (
												<div className="flex items-start gap-3 mb-4 border-b border-gray-200 pb-4">
													<div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
														<span className="text-sm">🤖</span>
													</div>
													<div className="flex-1">
														<p className="text-sm font-medium text-gray-800 mb-1">
															Chatbot says...
														</p>
														<p className="text-sm text-gray-700 whitespace-pre-wrap">
															{chatbotResponse}
														</p>
													</div>
												</div>
											)}

											<div className="flex gap-2">
												<Input
													placeholder="Ask Chatbot..."
													value={chatbotQuestion}
													onChange={(e) => setChatbotQuestion(e.target.value)}
													disabled={
														chatbotMutation.isPending ||
														!grokKeyStatus?.has_groq_api_key
													}
													className="flex-1"
													onKeyDown={(e) => {
														if (
															e.key === "Enter" &&
															grokKeyStatus?.has_groq_api_key
														) {
															handleChatbotSubmit();
														}
													}}
												/>
												<Button
													onClick={handleChatbotSubmit}
													disabled={
														chatbotMutation.isPending ||
														!chatbotQuestion.trim() ||
														!grokKeyStatus?.has_groq_api_key
													}
													className="bg-purple-600 hover:bg-purple-700"
												>
													{chatbotMutation.isPending ? (
														<div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
													) : (
														<Send size={16} />
													)}
												</Button>
											</div>
										</div>
									</div>
								)}
							</div>
						)}
					</div>
				)}

				<ReportMCQDialog
					open={isReportDialogOpen}
					onOpenChange={setIsReportDialogOpen}
					mcqId={mcq._id}
				/>
			</CardContent>
		</Card>
	);
};

const SavedPage = () => {
	const [activeTab, setActiveTab] = useState<"tests" | "mcqs">("mcqs");
	const { toast } = useToast();
	const isDesktop = useMediaQuery({ minWidth: 1024 });

	const [savedTests, setSavedTests] = useState<QuizItem[]>([]);
	const [testsLoading, setTestsLoading] = useState(true);

	const [savedMCQs, setSavedMCQs] = useState<QuizMCQ[]>([]);

	useEffect(() => {
		const fetchTests = async () => {
			try {
				setTestsLoading(true);
				const response = await getAllQuizzes(1, 20);
				const bookmarked = (response.data.data.quizzes || []).filter(
					(q) => q.bookmarked
				);
				setSavedTests(bookmarked);
			} catch {
				toast({
					title: "Error",
					description: "Failed to load saved tests.",
					variant: "destructive",
				});
			} finally {
				setTestsLoading(false);
			}
		};
		fetchTests();
	}, [toast]);

	const {
		data: savedMCQsData,
		isLoading: mcqsLoading,
		error: mcqsError,
	} = useGetBookmarks({ category: "mcq", page: 1, limit: 20 });

	// Update local MCQ state when data changes
	useEffect(() => {
		if (savedMCQsData?.mcqs) {
			setSavedMCQs(savedMCQsData.mcqs);
		}
	}, [savedMCQsData]);

	if (mcqsError) {
		toast({
			title: "Error",
			description: "Failed to load saved MCQs. Please try again.",
			variant: "destructive",
		});
	}

	// Handler to remove test from local state
	const handleRemoveTest = (testId: string) => {
		setSavedTests((prev) => prev.filter((test) => test._id !== testId));
	};

	// Handler to remove MCQ from local state
	const handleRemoveMCQ = (mcqId: string) => {
		setSavedMCQs((prev) => prev.filter((mcq) => mcq._id !== mcqId));
	};

	return (
		<>
			{!isDesktop && (
				<>
					<Navbar />
					<MobileSwitcher />
				</>
			)}

			<div className="container mx-auto">
				{!isDesktop && (
					<>
						<div className="sticky top-[56px] z-40 bg-white border-b border-gray-200">
							<MobileHeader title="Saved" />
							<div className="px-4 py-3 bg-white border-b border-gray-200">
								<Tabs
									value={activeTab}
									onValueChange={(v) => setActiveTab(v as any)}
								>
									<TabsList className="grid w-full grid-cols-2 bg-gray-100 h-10 p-1">
										<TabsTrigger
											value="mcqs"
											className="text-sm font-medium data-[state=active]:bg-white data-[state=active]:text-purple-600 data-[state=active]:shadow-sm"
										>
											Saved MCQ&apos;s
										</TabsTrigger>
										<TabsTrigger
											value="tests"
											className="text-sm font-medium data-[state=active]:bg-white data-[state=active]:text-purple-600 data-[state=active]:shadow-sm"
										>
											Saved Tests
										</TabsTrigger>
									</TabsList>
								</Tabs>
							</div>
						</div>
					</>
				)}

				<div
					className={`max-w-6xl mx-auto px-4 pb-24 ${
						!isDesktop ? "pt-4" : "pt-8"
					}`}
				>
					{isDesktop && (
						<>
							<h1 className="text-2xl font-bold text-gray-900 mb-6">Saved</h1>
							<div className="mb-6">
								<Tabs
									value={activeTab}
									onValueChange={(v) => setActiveTab(v as any)}
								>
									<TabsList className="flex w-full justify-start gap-2 bg-gray-50">
										<TabsTrigger
											value="mcqs"
											className="rounded-md data-[state=active]:bg-white data-[state=active]:border-2 data-[state=active]:border-purple-500 data-[state=active]:text-purple-600"
										>
											Saved MCQ&apos;s
										</TabsTrigger>
										<TabsTrigger
											value="tests"
											className="rounded-md data-[state=active]:bg-white data-[state=active]:border-2 data-[state=active]:border-purple-500 data-[state=active]:text-purple-600"
										>
											Saved Tests
										</TabsTrigger>
									</TabsList>
								</Tabs>
							</div>
						</>
					)}

					<Tabs
						value={activeTab}
						onValueChange={(v) => setActiveTab(v as any)}
						className="w-full"
					>
						{/* Saved MCQs */}
						<TabsContent value="mcqs" className="space-y-4 mt-0">
							{mcqsLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{[...Array(8)].map((_, i) => (
										<div
											key={i}
											className="h-32 bg-gray-200 animate-pulse rounded-lg"
										/>
									))}
								</div>
							) : savedMCQs.length > 0 ? (
								<div className="flex flex-col gap-6">
									{savedMCQs.map((mcq: QuizMCQ, index: number) => (
										<SavedMCQCard
											key={mcq._id}
											mcq={mcq}
											numbering={index}
											onRemove={handleRemoveMCQ}
										/>
									))}
								</div>
							) : (
								<div className="text-center py-12">
									<Bookmark className="w-10 h-10 text-[#D1D5DB] mx-auto mb-4" />
									<h3 className="text-[16px] font-semibold text-[#1F2937] mb-1">
										No saved MCQs
									</h3>
									<p className="text-[14px] text-[#6B7280]">
										Start bookmarking MCQs to see them here.
									</p>
								</div>
							)}
						</TabsContent>

						{/* Saved Tests */}
						<TabsContent value="tests" className="space-y-4 mt-0">
							{testsLoading ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
									{[...Array(8)].map((_, i) => (
										<div
											key={i}
											className="h-40 bg-gray-200 animate-pulse rounded-lg"
										/>
									))}
								</div>
							) : savedTests.length > 0 ? (
								<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
									{savedTests.map((test: QuizItem) => (
										<SavedTestCard
											key={test._id}
											test={test}
											onRemove={handleRemoveTest}
										/>
									))}
								</div>
							) : (
								<div className="text-center py-12">
									<Bookmark className="w-10 h-10 text-[#D1D5DB] mx-auto mb-4" />
									<h3 className="text-[16px] font-semibold text-[#1F2937] mb-1">
										No saved tests
									</h3>
									<p className="text-[14px] text-[#6B7280]">
										Start attempting and bookmarking tests to see them here.
									</p>
								</div>
							)}
						</TabsContent>
					</Tabs>
				</div>
			</div>
		</>
	);
};

export const Route = createFileRoute("/(app)/_mainlayout/saved")({
	component: SavedPage,
});
