import { subjects } from "../learning/constants";

export type MCQ = {
	id: string;
	question: string;
	options: string[];
	correctAnswer?: number; // Optional to handle cases where answer is not provided
	subject: (typeof subjects)[number]["id"];
	tag?: string;
	difficulty?: "easy" | "medium" | "hard";
	description?: string;
	topic?: string;
};

export type Test = {
	id: string;
	title: string;
	totalQuestions: number;
	duration: string;
	liveCheck: boolean;
	mcqs: MCQ[];
};

export type ReportReason =
	| "incorrect_answer"
	| "unclear_question"
	| "typo_error"
	| "inappropriate_content"
	| "duplicate_question"
	| "other";

export type ReportMCQPayload = {
	id: string;
	reasons: ReportReason[];
	comment: string;
};
